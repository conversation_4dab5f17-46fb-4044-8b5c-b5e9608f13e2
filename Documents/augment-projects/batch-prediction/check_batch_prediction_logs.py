#!/usr/bin/env python3
"""
Script to check logs and monitor Vertex AI Batch Prediction Jobs
"""

import time
import json
from google.cloud import aiplatform
from google.cloud import logging as cloud_logging
from google.cloud.aiplatform import BatchPredictionJob
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def check_batch_prediction_job_status(job_resource_name: str):
    """
    Check the status and basic information of a batch prediction job
    
    Args:
        job_resource_name: Full resource name of the batch prediction job
    """
    try:
        # Get the job object
        job = BatchPredictionJob(job_resource_name)
        
        logger.info(f"📊 Job Status Information:")
        logger.info(f"  Resource Name: {job.resource_name}")
        logger.info(f"  Display Name: {job.display_name}")
        logger.info(f"  State: {job.state}")
        logger.info(f"  Create Time: {job.create_time}")
        logger.info(f"  Update Time: {job.update_time}")
        
        if hasattr(job, 'start_time') and job.start_time:
            logger.info(f"  Start Time: {job.start_time}")
        
        if hasattr(job, 'end_time') and job.end_time:
            logger.info(f"  End Time: {job.end_time}")
        
        # Check if job has error information
        if hasattr(job, 'error') and job.error:
            logger.error(f"  ❌ Job Error: {job.error}")
        
        # Output information
        if hasattr(job, 'output_info') and job.output_info:
            logger.info(f"  📁 Output Info: {job.output_info}")
        
        return job
        
    except Exception as e:
        logger.error(f"❌ Error checking job status: {str(e)}")
        return None


def get_batch_prediction_logs_from_cloud_logging(project_id: str, job_display_name: str, hours_back: int = 24):
    """
    Retrieve logs from Google Cloud Logging for the batch prediction job
    
    Args:
        project_id: Google Cloud project ID
        job_display_name: Display name of the batch prediction job
        hours_back: How many hours back to search for logs
    """
    try:
        # Initialize Cloud Logging client
        client = cloud_logging.Client(project=project_id)
        
        # Calculate time filter
        from datetime import datetime, timedelta
        end_time = datetime.utcnow()
        start_time = end_time - timedelta(hours=hours_back)
        
        # Build the filter query
        filter_str = f'''
        resource.type="aiplatform.googleapis.com/BatchPredictionJob"
        AND jsonPayload.job_display_name="{job_display_name}"
        AND timestamp >= "{start_time.isoformat()}Z"
        AND timestamp <= "{end_time.isoformat()}Z"
        '''
        
        logger.info(f"🔍 Searching for logs with filter:")
        logger.info(f"  Job Display Name: {job_display_name}")
        logger.info(f"  Time Range: {start_time} to {end_time}")
        
        # Retrieve logs
        entries = client.list_entries(filter_=filter_str, order_by=cloud_logging.DESCENDING)
        
        log_count = 0
        for entry in entries:
            log_count += 1
            logger.info(f"\n📝 Log Entry #{log_count}:")
            logger.info(f"  Timestamp: {entry.timestamp}")
            logger.info(f"  Severity: {entry.severity}")
            logger.info(f"  Log Name: {entry.log_name}")
            
            if hasattr(entry, 'json_payload') and entry.json_payload:
                logger.info(f"  Payload: {json.dumps(entry.json_payload, indent=2)}")
            elif hasattr(entry, 'text_payload') and entry.text_payload:
                logger.info(f"  Message: {entry.text_payload}")
            
            # Limit to first 10 entries to avoid spam
            if log_count >= 10:
                logger.info(f"  ... (showing first 10 entries)")
                break
        
        if log_count == 0:
            logger.warning(f"⚠️ No logs found for job '{job_display_name}' in the last {hours_back} hours")
            
        return log_count
        
    except Exception as e:
        logger.error(f"❌ Error retrieving logs: {str(e)}")
        return 0


def monitor_batch_prediction_job(job_resource_name: str, check_interval: int = 60, max_checks: int = 60):
    """
    Monitor a batch prediction job and print status updates
    
    Args:
        job_resource_name: Full resource name of the batch prediction job
        check_interval: Seconds between status checks
        max_checks: Maximum number of status checks
    """
    logger.info(f"🔄 Starting to monitor job: {job_resource_name}")
    logger.info(f"  Check interval: {check_interval} seconds")
    logger.info(f"  Max checks: {max_checks}")
    
    for check_num in range(max_checks):
        try:
            job = BatchPredictionJob(job_resource_name)
            
            logger.info(f"\n📊 Check #{check_num + 1} - Job Status:")
            logger.info(f"  State: {job.state}")
            logger.info(f"  Update Time: {job.update_time}")
            
            # Check if job is completed
            if job.state.name in ['JOB_STATE_SUCCEEDED', 'JOB_STATE_FAILED', 'JOB_STATE_CANCELLED']:
                logger.info(f"✅ Job completed with state: {job.state.name}")
                
                if job.state.name == 'JOB_STATE_SUCCEEDED':
                    logger.info(f"🎉 Job succeeded!")
                    if hasattr(job, 'output_info') and job.output_info:
                        logger.info(f"📁 Output location: {job.output_info}")
                elif job.state.name == 'JOB_STATE_FAILED':
                    logger.error(f"❌ Job failed!")
                    if hasattr(job, 'error') and job.error:
                        logger.error(f"Error details: {job.error}")
                
                break
            
            # Wait before next check
            if check_num < max_checks - 1:
                logger.info(f"⏳ Waiting {check_interval} seconds before next check...")
                time.sleep(check_interval)
                
        except Exception as e:
            logger.error(f"❌ Error during monitoring check #{check_num + 1}: {str(e)}")
            time.sleep(check_interval)
    
    else:
        logger.warning(f"⚠️ Reached maximum number of checks ({max_checks}). Job may still be running.")


def get_job_resource_name_from_display_name(project_id: str, location: str, display_name: str):
    """
    Find a batch prediction job by display name and return its resource name
    
    Args:
        project_id: Google Cloud project ID
        location: Location (e.g., 'us-central1')
        display_name: Display name of the job
    """
    try:
        aiplatform.init(project=project_id, location=location)
        
        # List all batch prediction jobs
        jobs = aiplatform.BatchPredictionJob.list()
        
        logger.info(f"🔍 Searching for job with display name: '{display_name}'")
        
        for job in jobs:
            if job.display_name == display_name:
                logger.info(f"✅ Found job: {job.resource_name}")
                return job.resource_name
        
        logger.warning(f"⚠️ No job found with display name: '{display_name}'")
        
        # List all jobs for reference
        logger.info(f"📋 Available jobs:")
        for job in jobs[:10]:  # Show first 10 jobs
            logger.info(f"  - {job.display_name} ({job.state.name}) - {job.resource_name}")
        
        return None
        
    except Exception as e:
        logger.error(f"❌ Error searching for job: {str(e)}")
        return None


def comprehensive_job_check(project_id: str, location: str, job_display_name: str):
    """
    Comprehensive check of a batch prediction job including status and logs
    
    Args:
        project_id: Google Cloud project ID
        location: Location (e.g., 'us-central1')
        job_display_name: Display name of the job
    """
    logger.info(f"🚀 Starting comprehensive check for job: '{job_display_name}'")
    
    # Initialize Vertex AI
    aiplatform.init(project=project_id, location=location)
    
    # Step 1: Find the job by display name
    job_resource_name = get_job_resource_name_from_display_name(project_id, location, job_display_name)
    
    if not job_resource_name:
        logger.error(f"❌ Could not find job with display name: '{job_display_name}'")
        return
    
    # Step 2: Check job status
    logger.info(f"\n" + "="*50)
    logger.info(f"STEP 1: CHECKING JOB STATUS")
    logger.info(f"="*50)
    job = check_batch_prediction_job_status(job_resource_name)
    
    # Step 3: Get logs from Cloud Logging
    logger.info(f"\n" + "="*50)
    logger.info(f"STEP 2: RETRIEVING CLOUD LOGS")
    logger.info(f"="*50)
    log_count = get_batch_prediction_logs_from_cloud_logging(project_id, job_display_name)
    
    # Step 4: If job is still running, offer to monitor
    if job and job.state.name not in ['JOB_STATE_SUCCEEDED', 'JOB_STATE_FAILED', 'JOB_STATE_CANCELLED']:
        logger.info(f"\n" + "="*50)
        logger.info(f"STEP 3: JOB IS STILL RUNNING")
        logger.info(f"="*50)
        logger.info(f"Job is in state: {job.state.name}")
        logger.info(f"You can monitor the job by calling:")
        logger.info(f"monitor_batch_prediction_job('{job_resource_name}')")


# Example usage
if __name__ == "__main__":
    # Your specific parameters
    PROJECT_ID = "labs-science"
    LOCATION = "us-central1"
    JOB_DISPLAY_NAME = "user-embedding-batch-prediction"
    
    print("🔍 Batch Prediction Job Log Checker")
    print("="*60)
    
    # Run comprehensive check
    comprehensive_job_check(PROJECT_ID, LOCATION, JOB_DISPLAY_NAME)
    
    print("\n" + "="*60)
    print("💡 Additional Commands:")
    print("="*60)
    print("To monitor a specific job:")
    print("  monitor_batch_prediction_job('projects/.../batchPredictionJobs/...')")
    print("\nTo check logs for a different time range:")
    print("  get_batch_prediction_logs_from_cloud_logging('labs-science', 'job-name', hours_back=48)")
