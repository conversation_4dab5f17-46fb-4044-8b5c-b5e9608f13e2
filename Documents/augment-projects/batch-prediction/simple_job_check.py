#!/usr/bin/env python3
"""
Simple script to check your batch prediction job status and logs
"""

from google.cloud import aiplatform
import time

# Your specific parameters
PROJECT_ID = "labs-science"
LOCATION = "us-central1"
JOB_DISPLAY_NAME = "user-embedding-batch-prediction"

def check_your_job():
    """Check your specific batch prediction job"""
    
    print("🔍 Checking your batch prediction job...")
    print(f"Project: {PROJECT_ID}")
    print(f"Location: {LOCATION}")
    print(f"Looking for job: {JOB_DISPLAY_NAME}")
    print("=" * 60)
    
    # Initialize Vertex AI
    aiplatform.init(project=PROJECT_ID, location=LOCATION)
    
    try:
        # List all batch prediction jobs
        jobs = aiplatform.BatchPredictionJob.list()
        
        print(f"📋 Found {len(jobs)} total batch prediction jobs")
        print("\n🔍 All your recent jobs:")
        
        your_job = None
        for i, job in enumerate(jobs[:10]):  # Show first 10 jobs
            status_emoji = {
                'JOB_STATE_SUCCEEDED': '✅',
                'JOB_STATE_FAILED': '❌', 
                'JOB_STATE_RUNNING': '🔄',
                'JOB_STATE_PENDING': '⏳',
                'JOB_STATE_CANCELLED': '🚫'
            }.get(job.state.name, '❓')
            
            print(f"  {i+1}. {status_emoji} {job.display_name}")
            print(f"     State: {job.state.name}")
            print(f"     Created: {job.create_time}")
            print(f"     Resource: {job.resource_name}")
            
            # Check if this is your job
            if job.display_name == JOB_DISPLAY_NAME:
                your_job = job
                print(f"     👆 THIS IS YOUR JOB!")
            print()
        
        if your_job:
            print("=" * 60)
            print("🎯 YOUR JOB DETAILS:")
            print("=" * 60)
            print(f"Display Name: {your_job.display_name}")
            print(f"Resource Name: {your_job.resource_name}")
            print(f"State: {your_job.state.name}")
            print(f"Created: {your_job.create_time}")
            print(f"Updated: {your_job.update_time}")
            
            if hasattr(your_job, 'start_time') and your_job.start_time:
                print(f"Started: {your_job.start_time}")
            
            if hasattr(your_job, 'end_time') and your_job.end_time:
                print(f"Ended: {your_job.end_time}")
            
            # Check job status
            if your_job.state.name == 'JOB_STATE_SUCCEEDED':
                print("\n🎉 SUCCESS! Your job completed successfully!")
                if hasattr(your_job, 'output_info') and your_job.output_info:
                    print(f"📁 Output location: {your_job.output_info}")
                    
            elif your_job.state.name == 'JOB_STATE_FAILED':
                print("\n❌ FAILED! Your job failed.")
                if hasattr(your_job, 'error') and your_job.error:
                    print(f"🚨 Error details: {your_job.error}")
                    
            elif your_job.state.name == 'JOB_STATE_RUNNING':
                print("\n🔄 RUNNING! Your job is currently running...")
                print("💡 You can monitor it by running this script again")
                
            elif your_job.state.name == 'JOB_STATE_PENDING':
                print("\n⏳ PENDING! Your job is waiting to start...")
                
            else:
                print(f"\n📊 Status: {your_job.state.name}")
            
            return your_job
        else:
            print("❌ Could not find your specific job!")
            print(f"Looking for job with display name: '{JOB_DISPLAY_NAME}'")
            return None
            
    except Exception as e:
        print(f"❌ Error checking jobs: {str(e)}")
        return None


def monitor_job(job):
    """Simple monitoring of a job"""
    if not job:
        return
    
    print("\n" + "=" * 60)
    print("🔄 MONITORING OPTIONS:")
    print("=" * 60)
    
    print(f"To wait for your job to complete (blocking), use:")
    print(f"```python")
    print(f"from google.cloud import aiplatform")
    print(f"aiplatform.init(project='{PROJECT_ID}', location='{LOCATION}')")
    print(f"job = aiplatform.BatchPredictionJob('{job.resource_name}')")
    print(f"job.wait()  # This will wait until job completes")
    print(f"print(f'Job completed with state: {{job.state.name}}')")
    print(f"```")
    
    print(f"\nTo check status periodically:")
    print(f"```python")
    print(f"import time")
    print(f"job = aiplatform.BatchPredictionJob('{job.resource_name}')")
    print(f"while job.state.name not in ['JOB_STATE_SUCCEEDED', 'JOB_STATE_FAILED']:")
    print(f"    print(f'Current state: {{job.state.name}}')")
    print(f"    time.sleep(60)  # Wait 1 minute")
    print(f"    job.refresh()  # Refresh job status")
    print(f"```")


def check_gcs_output(job):
    """Check if output files exist in GCS"""
    if not job or job.state.name != 'JOB_STATE_SUCCEEDED':
        return
    
    print("\n" + "=" * 60)
    print("📁 CHECKING OUTPUT FILES:")
    print("=" * 60)
    
    try:
        if hasattr(job, 'output_info') and job.output_info:
            output_path = job.output_info.gcs_output_directory
            print(f"Output directory: {output_path}")
            
            # You can use gsutil to list files:
            print(f"\n💡 To see output files, run:")
            print(f"gsutil ls {output_path}")
            print(f"gsutil ls {output_path}predictions_*.jsonl")
            
    except Exception as e:
        print(f"❌ Error checking output: {str(e)}")


if __name__ == "__main__":
    print("🚀 Simple Batch Prediction Job Checker")
    print("This will check your specific job and show its status")
    print()
    
    # Check your job
    job = check_your_job()
    
    # Show monitoring options
    monitor_job(job)
    
    # Check output if job succeeded
    check_gcs_output(job)
    
    print("\n" + "=" * 60)
    print("✅ Check completed!")
    print("💡 Run this script again to refresh the status")
    print("=" * 60)
