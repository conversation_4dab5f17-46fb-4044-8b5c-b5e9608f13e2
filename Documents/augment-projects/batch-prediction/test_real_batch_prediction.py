#!/usr/bin/env python3
"""
Test script for aiplatform.BatchPredictionJob.create with real parameters
This script demonstrates the actual function call with real parameter values
extracted from the existing codebase.
"""

import os
from google.cloud import aiplatform
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_batch_prediction_job_creation():
    """
    Test function that creates a batch prediction job with real parameters
    """
    
    # Real parameters extracted from customized_batchprediction.py
    PROJECT_ID = "labs-science"
    LOCATION = "us-central1"
    
    # Initialize Vertex AI
    aiplatform.init(project=PROJECT_ID, location=LOCATION)
    
    # Real parameter values
    job_display_name = "test-user-embedding-batch-prediction"
    model_resource_name = "projects/261231409699/locations/us-central1/models/7294814348084510720@1"
    input_gcs_path = "gs://zhang_us_central/batch_prediction/test_input.jsonl"
    output_gcs_path = "gs://zhang_us_central/batch_prediction/test_output/"
    
    logger.info("Creating batch prediction job with the following parameters:")
    logger.info(f"  Project ID: {PROJECT_ID}")
    logger.info(f"  Location: {LOCATION}")
    logger.info(f"  Job Display Name: {job_display_name}")
    logger.info(f"  Model Resource Name: {model_resource_name}")
    logger.info(f"  Input GCS Path: {input_gcs_path}")
    logger.info(f"  Output GCS Path: {output_gcs_path}")
    
    try:
        # This is the actual function call with real parameters
        batch_prediction_job = aiplatform.BatchPredictionJob.create(
            job_display_name=job_display_name,
            model_name=model_resource_name,
            gcs_source=input_gcs_path,
            gcs_destination_prefix=output_gcs_path,
            machine_type="n1-standard-4",
            accelerator_type=None,  # CPU only for ONNX
            accelerator_count=0,
            starting_replica_count=1,
            max_replica_count=10,
            sync=False  # Run asynchronously
        )
        
        logger.info(f"✅ Batch prediction job created successfully!")
        logger.info(f"Job Resource Name: {batch_prediction_job.resource_name}")
        logger.info(f"Job Display Name: {batch_prediction_job.display_name}")
        logger.info(f"Job State: {batch_prediction_job.state}")
        
        return batch_prediction_job
        
    except Exception as e:
        logger.error(f"❌ Failed to create batch prediction job: {str(e)}")
        raise


def test_batch_prediction_with_different_configurations():
    """
    Test batch prediction job creation with different machine configurations
    """
    
    PROJECT_ID = "labs-science"
    LOCATION = "us-central1"
    
    aiplatform.init(project=PROJECT_ID, location=LOCATION)
    
    # Test configurations
    test_configs = [
        {
            "name": "cpu-small",
            "machine_type": "n1-standard-2",
            "accelerator_type": None,
            "accelerator_count": 0,
            "max_replica_count": 5
        },
        {
            "name": "cpu-medium", 
            "machine_type": "n1-standard-4",
            "accelerator_type": None,
            "accelerator_count": 0,
            "max_replica_count": 10
        },
        {
            "name": "cpu-large",
            "machine_type": "n1-standard-8",
            "accelerator_type": None,
            "accelerator_count": 0,
            "max_replica_count": 15
        },
        {
            "name": "gpu-enabled",
            "machine_type": "n1-standard-4",
            "accelerator_type": "NVIDIA_TESLA_T4",
            "accelerator_count": 1,
            "max_replica_count": 5
        }
    ]
    
    base_model_name = "projects/261231409699/locations/us-central1/models/7294814348084510720@1"
    base_input_path = "gs://zhang_us_central/batch_prediction/test_input.jsonl"
    
    for config in test_configs:
        logger.info(f"\n🧪 Testing configuration: {config['name']}")
        
        job_display_name = f"test-batch-prediction-{config['name']}"
        output_gcs_path = f"gs://zhang_us_central/batch_prediction/test_output_{config['name']}/"
        
        logger.info(f"Parameters for {config['name']}:")
        logger.info(f"  job_display_name: {job_display_name}")
        logger.info(f"  model_name: {base_model_name}")
        logger.info(f"  gcs_source: {base_input_path}")
        logger.info(f"  gcs_destination_prefix: {output_gcs_path}")
        logger.info(f"  machine_type: {config['machine_type']}")
        logger.info(f"  accelerator_type: {config['accelerator_type']}")
        logger.info(f"  accelerator_count: {config['accelerator_count']}")
        logger.info(f"  starting_replica_count: 1")
        logger.info(f"  max_replica_count: {config['max_replica_count']}")
        logger.info(f"  sync: False")
        
        # Note: Actual job creation is commented out to avoid creating real jobs
        # Uncomment the following block to create real jobs
        """
        try:
            batch_prediction_job = aiplatform.BatchPredictionJob.create(
                job_display_name=job_display_name,
                model_name=base_model_name,
                gcs_source=base_input_path,
                gcs_destination_prefix=output_gcs_path,
                machine_type=config['machine_type'],
                accelerator_type=config['accelerator_type'],
                accelerator_count=config['accelerator_count'],
                starting_replica_count=1,
                max_replica_count=config['max_replica_count'],
                sync=False
            )
            
            logger.info(f"✅ {config['name']} job created: {batch_prediction_job.resource_name}")
            
        except Exception as e:
            logger.error(f"❌ Failed to create {config['name']} job: {str(e)}")
        """


def print_parameter_template():
    """
    Print a template showing all the parameters for BatchPredictionJob.create
    """
    
    print("\n" + "="*80)
    print("BATCH PREDICTION JOB PARAMETER TEMPLATE")
    print("="*80)
    
    template = """
# Real parameters from the codebase:
batch_prediction_job = aiplatform.BatchPredictionJob.create(
    job_display_name="user-embedding-batch-prediction",           # String: Display name for the job
    model_name="projects/261231409699/locations/us-central1/models/7294814348084510720@1",  # String: Full model resource name
    gcs_source="gs://zhang_us_central/batch_prediction/input.jsonl",     # String: Input data location
    gcs_destination_prefix="gs://zhang_us_central/batch_prediction/output/",  # String: Output location prefix
    machine_type="n1-standard-4",                                # String: Machine type for prediction
    accelerator_type=None,                                        # String or None: GPU type (None for CPU)
    accelerator_count=0,                                          # Integer: Number of accelerators
    starting_replica_count=1,                                     # Integer: Initial number of replicas
    max_replica_count=10,                                         # Integer: Maximum number of replicas
    sync=False                                                    # Boolean: Whether to wait for completion
)

# Alternative configurations:

# For GPU-enabled prediction:
batch_prediction_job = aiplatform.BatchPredictionJob.create(
    job_display_name="gpu-batch-prediction",
    model_name="projects/261231409699/locations/us-central1/models/7294814348084510720@1",
    gcs_source="gs://zhang_us_central/batch_prediction/input.jsonl",
    gcs_destination_prefix="gs://zhang_us_central/batch_prediction/output/",
    machine_type="n1-standard-4",
    accelerator_type="NVIDIA_TESLA_T4",                          # GPU type
    accelerator_count=1,                                         # Number of GPUs
    starting_replica_count=1,
    max_replica_count=5,
    sync=False
)

# For synchronous execution (wait for completion):
batch_prediction_job = aiplatform.BatchPredictionJob.create(
    job_display_name="sync-batch-prediction",
    model_name="projects/261231409699/locations/us-central1/models/7294814348084510720@1",
    gcs_source="gs://zhang_us_central/batch_prediction/input.jsonl",
    gcs_destination_prefix="gs://zhang_us_central/batch_prediction/output/",
    machine_type="n1-standard-4",
    accelerator_type=None,
    accelerator_count=0,
    starting_replica_count=1,
    max_replica_count=10,
    sync=True                                                    # Wait for completion
)
"""
    
    print(template)
    print("="*80)


if __name__ == "__main__":
    print("🚀 Batch Prediction Job Test Script")
    print("This script demonstrates aiplatform.BatchPredictionJob.create with real parameters")
    
    # Print parameter template
    print_parameter_template()
    
    # Test different configurations (without actually creating jobs)
    print("\n📋 Testing different configurations...")
    test_batch_prediction_with_different_configurations()
    
    # Uncomment the following line to actually create a test job
    # test_batch_prediction_job_creation()
    
    print("\n✅ Test script completed!")
    print("💡 To actually create jobs, uncomment the function calls in the script.")
