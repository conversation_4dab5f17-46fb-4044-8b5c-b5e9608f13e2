#!/usr/bin/env python3
"""
Script to find your specific batch prediction job by input/output paths
"""

from google.cloud import aiplatform
from datetime import datetime, timedelta

# Your specific parameters from the job creation
PROJECT_ID = "labs-science"
LOCATION = "us-central1"
INPUT_GCS_PATH = "gs://zhang_us_central/batch_prediction/input_20250617_021717.jsonl"
OUTPUT_GCS_PATH = "gs://zhang_us_central/batch_prediction/output/"
MODEL_NAME = "projects/261231409699/locations/us-central1/models/7294814348084510720@1"

def find_job_by_parameters():
    """Find your job by matching the input/output paths and model"""
    
    print("🔍 Finding your batch prediction job by parameters...")
    print(f"Looking for job with:")
    print(f"  Input: {INPUT_GCS_PATH}")
    print(f"  Output prefix: {OUTPUT_GCS_PATH}")
    print(f"  Model: {MODEL_NAME}")
    print("=" * 80)
    
    # Initialize Vertex AI
    aiplatform.init(project=PROJECT_ID, location=LOCATION)
    
    try:
        # Get all batch prediction jobs
        jobs = aiplatform.BatchPredictionJob.list()
        
        print(f"📋 Checking {len(jobs)} total jobs...")
        
        matching_jobs = []
        recent_jobs = []
        
        # Check jobs from the last 24 hours first
        now = datetime.now().replace(tzinfo=None)
        yesterday = now - timedelta(days=1)
        
        for job in jobs:
            # Convert job create time to naive datetime for comparison
            job_time = job.create_time.replace(tzinfo=None)
            
            # Check if job is from today (based on your input filename timestamp)
            if "20250617" in INPUT_GCS_PATH and job_time.date() == datetime(2025, 6, 17).date():
                recent_jobs.append(job)
                print(f"\n📅 Job from June 17, 2025:")
                print(f"  Display Name: {job.display_name}")
                print(f"  State: {job.state.name}")
                print(f"  Created: {job.create_time}")
                print(f"  Resource: {job.resource_name}")
                
                # Try to get job details to check input/output
                try:
                    # Check if this could be your job by examining the job details
                    if hasattr(job, 'input_config') and job.input_config:
                        print(f"  Input Config: {job.input_config}")
                    
                    if hasattr(job, 'output_config') and job.output_config:
                        print(f"  Output Config: {job.output_config}")
                    
                    if hasattr(job, 'model_name') and job.model_name:
                        print(f"  Model: {job.model_name}")
                        if job.model_name == MODEL_NAME:
                            print(f"  ✅ MODEL MATCHES!")
                            matching_jobs.append(job)
                    
                except Exception as e:
                    print(f"  ⚠️ Could not get detailed info: {e}")
        
        if matching_jobs:
            print("\n" + "=" * 80)
            print("🎯 FOUND MATCHING JOBS!")
            print("=" * 80)
            
            for job in matching_jobs:
                print_job_details(job)
                
        elif recent_jobs:
            print("\n" + "=" * 80)
            print("📅 JOBS FROM TODAY (June 17, 2025):")
            print("=" * 80)
            print("These might be your job with a different display name:")
            
            for job in recent_jobs:
                print_job_details(job)
                
        else:
            print("\n❌ No jobs found matching your criteria")
            print("Let's check the most recent jobs:")
            
            for i, job in enumerate(jobs[:5]):
                print(f"\n{i+1}. Recent Job:")
                print_job_details(job)
        
        return matching_jobs if matching_jobs else recent_jobs
        
    except Exception as e:
        print(f"❌ Error searching for jobs: {str(e)}")
        return []


def print_job_details(job):
    """Print detailed information about a job"""
    
    status_emoji = {
        'JOB_STATE_SUCCEEDED': '✅',
        'JOB_STATE_FAILED': '❌', 
        'JOB_STATE_RUNNING': '🔄',
        'JOB_STATE_PENDING': '⏳',
        'JOB_STATE_CANCELLED': '🚫'
    }.get(job.state.name, '❓')
    
    print(f"\n{status_emoji} Job: {job.display_name}")
    print(f"   State: {job.state.name}")
    print(f"   Created: {job.create_time}")
    print(f"   Updated: {job.update_time}")
    print(f"   Resource: {job.resource_name}")
    
    # Show status-specific information
    if job.state.name == 'JOB_STATE_SUCCEEDED':
        print(f"   🎉 SUCCESS!")
        if hasattr(job, 'output_info') and job.output_info:
            print(f"   📁 Output: {job.output_info}")
            
    elif job.state.name == 'JOB_STATE_FAILED':
        print(f"   ❌ FAILED!")
        if hasattr(job, 'error') and job.error:
            print(f"   🚨 Error: {job.error}")
            
    elif job.state.name == 'JOB_STATE_RUNNING':
        print(f"   🔄 RUNNING...")
        
    elif job.state.name == 'JOB_STATE_PENDING':
        print(f"   ⏳ PENDING...")


def monitor_job_by_resource_name(resource_name):
    """Monitor a specific job by its resource name"""
    
    print(f"\n🔄 Monitoring job: {resource_name}")
    print("-" * 60)
    
    try:
        job = aiplatform.BatchPredictionJob(resource_name)
        
        print(f"Current state: {job.state.name}")
        print(f"Last updated: {job.update_time}")
        
        if job.state.name == 'JOB_STATE_RUNNING':
            print("\n💡 Your job is still running. To wait for completion:")
            print(f"```python")
            print(f"from google.cloud import aiplatform")
            print(f"aiplatform.init(project='{PROJECT_ID}', location='{LOCATION}')")
            print(f"job = aiplatform.BatchPredictionJob('{resource_name}')")
            print(f"job.wait()  # This will block until completion")
            print(f"print('Job completed with state:', job.state.name)")
            print(f"```")
        
        return job
        
    except Exception as e:
        print(f"❌ Error monitoring job: {str(e)}")
        return None


def create_new_job_if_needed():
    """Show how to create a new job if the previous one wasn't found"""
    
    print("\n" + "=" * 80)
    print("🚀 CREATE NEW JOB (if needed):")
    print("=" * 80)
    
    print("If you need to create a new batch prediction job, use:")
    print(f"```python")
    print(f"from google.cloud import aiplatform")
    print(f"")
    print(f"aiplatform.init(project='{PROJECT_ID}', location='{LOCATION}')")
    print(f"")
    print(f"batch_prediction_job = aiplatform.BatchPredictionJob.create(")
    print(f"    job_display_name='user-embedding-batch-prediction-new',")
    print(f"    model_name='{MODEL_NAME}',")
    print(f"    gcs_source='{INPUT_GCS_PATH}',")
    print(f"    gcs_destination_prefix='{OUTPUT_GCS_PATH}',")
    print(f"    machine_type='n1-standard-4',")
    print(f"    accelerator_type=None,")
    print(f"    accelerator_count=0,")
    print(f"    starting_replica_count=1,")
    print(f"    max_replica_count=10,")
    print(f"    sync=False")
    print(f")")
    print(f"")
    print(f"print('Job created:', batch_prediction_job.resource_name)")
    print(f"```")


if __name__ == "__main__":
    print("🔍 Finding Your Batch Prediction Job")
    print("This script will search for your job by matching parameters")
    print()
    
    # Find jobs by parameters
    found_jobs = find_job_by_parameters()
    
    # If jobs found, show monitoring options
    if found_jobs:
        print(f"\n💡 To monitor any of these jobs, copy the resource name and use:")
        print(f"monitor_job_by_resource_name('projects/.../batchPredictionJobs/...')")
    
    # Show how to create new job if needed
    create_new_job_if_needed()
    
    print("\n" + "=" * 80)
    print("✅ Search completed!")
    print("=" * 80)
