import unittest
from unittest.mock import Mock, patch, MagicMock
import pytest
from google.cloud import aiplatform
from customized_batchprediction import VertexAIBatchPredictor


class TestBatchPredictionJob(unittest.TestCase):
    """Test cases for aiplatform.BatchPredictionJob.create function"""
    
    def setUp(self):
        """Set up test fixtures with real parameter values"""
        self.project_id = "labs-science"
        self.location = "us-central1"
        self.model_resource_name = "projects/261231409699/locations/us-central1/models/7294814348084510720@1"
        self.input_gcs_path = "gs://zhang_us_central/batch_prediction/input_20241217_143022.jsonl"
        self.output_gcs_path = "gs://zhang_us_central/batch_prediction/output_20241217_143022/"
        self.job_display_name = "user-embedding-batch-prediction-test"
        
        # Initialize the predictor
        with patch('google.cloud.aiplatform.init'):
            with patch('google.cloud.bigquery.Client'):
                with patch('google.cloud.storage.Client'):
                    self.predictor = VertexAIBatchPredictor(self.project_id, self.location)

    @patch('google.cloud.aiplatform.BatchPredictionJob.create')
    def test_batch_prediction_job_create_with_real_parameters(self, mock_create):
        """Test BatchPredictionJob.create with real parameter values"""
        
        # Mock the return value
        mock_job = Mock()
        mock_job.resource_name = f"projects/{self.project_id}/locations/{self.location}/batchPredictionJobs/123456789"
        mock_create.return_value = mock_job
        
        # Call the function with real parameters
        result = self.predictor.create_batch_prediction_job(
            model_resource_name=self.model_resource_name,
            input_gcs_path=self.input_gcs_path,
            output_gcs_path=self.output_gcs_path,
            job_display_name=self.job_display_name
        )
        
        # Verify the function was called with correct parameters
        mock_create.assert_called_once_with(
            job_display_name=self.job_display_name,
            model_name=self.model_resource_name,
            gcs_source=self.input_gcs_path,
            gcs_destination_prefix=self.output_gcs_path,
            machine_type="n1-standard-4",
            accelerator_type=None,
            accelerator_count=0,
            starting_replica_count=1,
            max_replica_count=10,
            sync=False
        )
        
        # Verify the return value
        self.assertEqual(result, mock_job.resource_name)

    @patch('google.cloud.aiplatform.BatchPredictionJob.create')
    def test_batch_prediction_job_create_direct_call(self, mock_create):
        """Test direct call to aiplatform.BatchPredictionJob.create with real parameters"""
        
        # Mock the return value
        mock_job = Mock()
        mock_job.resource_name = f"projects/{self.project_id}/locations/{self.location}/batchPredictionJobs/987654321"
        mock_create.return_value = mock_job
        
        # Direct call to the function with real parameters
        batch_prediction_job = aiplatform.BatchPredictionJob.create(
            job_display_name=self.job_display_name,
            model_name=self.model_resource_name,
            gcs_source=self.input_gcs_path,
            gcs_destination_prefix=self.output_gcs_path,
            machine_type="n1-standard-4",
            accelerator_type=None,  # CPU only for ONNX
            accelerator_count=0,
            starting_replica_count=1,
            max_replica_count=10,
            sync=False  # Run asynchronously
        )
        
        # Verify the function was called correctly
        mock_create.assert_called_once_with(
            job_display_name=self.job_display_name,
            model_name=self.model_resource_name,
            gcs_source=self.input_gcs_path,
            gcs_destination_prefix=self.output_gcs_path,
            machine_type="n1-standard-4",
            accelerator_type=None,
            accelerator_count=0,
            starting_replica_count=1,
            max_replica_count=10,
            sync=False
        )
        
        # Verify the return value
        self.assertEqual(batch_prediction_job.resource_name, mock_job.resource_name)

    def test_parameter_validation(self):
        """Test parameter validation for batch prediction job creation"""
        
        # Test with different machine types
        valid_machine_types = [
            "n1-standard-4",
            "n1-standard-8", 
            "n1-standard-16",
            "n1-highmem-2",
            "n1-highmem-4"
        ]
        
        for machine_type in valid_machine_types:
            with patch('google.cloud.aiplatform.BatchPredictionJob.create') as mock_create:
                mock_job = Mock()
                mock_job.resource_name = f"projects/{self.project_id}/locations/{self.location}/batchPredictionJobs/test"
                mock_create.return_value = mock_job
                
                # Test with different machine types
                aiplatform.BatchPredictionJob.create(
                    job_display_name=f"test-job-{machine_type}",
                    model_name=self.model_resource_name,
                    gcs_source=self.input_gcs_path,
                    gcs_destination_prefix=self.output_gcs_path,
                    machine_type=machine_type,
                    accelerator_type=None,
                    accelerator_count=0,
                    starting_replica_count=1,
                    max_replica_count=10,
                    sync=False
                )
                
                mock_create.assert_called_once()

    @patch('google.cloud.aiplatform.BatchPredictionJob.create')
    def test_batch_prediction_with_gpu_parameters(self, mock_create):
        """Test batch prediction job creation with GPU parameters"""
        
        mock_job = Mock()
        mock_job.resource_name = f"projects/{self.project_id}/locations/{self.location}/batchPredictionJobs/gpu_test"
        mock_create.return_value = mock_job
        
        # Test with GPU configuration
        batch_prediction_job = aiplatform.BatchPredictionJob.create(
            job_display_name="gpu-batch-prediction-test",
            model_name=self.model_resource_name,
            gcs_source=self.input_gcs_path,
            gcs_destination_prefix=self.output_gcs_path,
            machine_type="n1-standard-4",
            accelerator_type="NVIDIA_TESLA_T4",
            accelerator_count=1,
            starting_replica_count=1,
            max_replica_count=5,
            sync=False
        )
        
        mock_create.assert_called_once_with(
            job_display_name="gpu-batch-prediction-test",
            model_name=self.model_resource_name,
            gcs_source=self.input_gcs_path,
            gcs_destination_prefix=self.output_gcs_path,
            machine_type="n1-standard-4",
            accelerator_type="NVIDIA_TESLA_T4",
            accelerator_count=1,
            starting_replica_count=1,
            max_replica_count=5,
            sync=False
        )


class TestBatchPredictionIntegration(unittest.TestCase):
    """Integration tests for batch prediction functionality"""
    
    def setUp(self):
        """Set up integration test fixtures"""
        self.real_parameters = {
            "project_id": "labs-science",
            "location": "us-central1",
            "model_resource_name": "projects/261231409699/locations/us-central1/models/7294814348084510720@1",
            "input_gcs_path": "gs://zhang_us_central/batch_prediction/input_test.jsonl",
            "output_gcs_path": "gs://zhang_us_central/batch_prediction/output_test/",
            "job_display_name": "integration-test-batch-prediction"
        }

    @patch('google.cloud.aiplatform.init')
    @patch('google.cloud.bigquery.Client')
    @patch('google.cloud.storage.Client')
    @patch('google.cloud.aiplatform.BatchPredictionJob.create')
    def test_end_to_end_batch_prediction_workflow(self, mock_create, mock_storage, mock_bq, mock_init):
        """Test the complete batch prediction workflow with real parameters"""
        
        # Mock the batch prediction job
        mock_job = Mock()
        mock_job.resource_name = f"projects/{self.real_parameters['project_id']}/locations/{self.real_parameters['location']}/batchPredictionJobs/integration_test"
        mock_create.return_value = mock_job
        
        # Initialize predictor
        predictor = VertexAIBatchPredictor(
            self.real_parameters["project_id"], 
            self.real_parameters["location"]
        )
        
        # Test batch prediction job creation
        result = predictor.create_batch_prediction_job(
            model_resource_name=self.real_parameters["model_resource_name"],
            input_gcs_path=self.real_parameters["input_gcs_path"],
            output_gcs_path=self.real_parameters["output_gcs_path"],
            job_display_name=self.real_parameters["job_display_name"]
        )
        
        # Verify the job was created with correct parameters
        mock_create.assert_called_once_with(
            job_display_name=self.real_parameters["job_display_name"],
            model_name=self.real_parameters["model_resource_name"],
            gcs_source=self.real_parameters["input_gcs_path"],
            gcs_destination_prefix=self.real_parameters["output_gcs_path"],
            machine_type="n1-standard-4",
            accelerator_type=None,
            accelerator_count=0,
            starting_replica_count=1,
            max_replica_count=10,
            sync=False
        )
        
        self.assertEqual(result, mock_job.resource_name)


def create_sample_batch_prediction_job():
    """
    Sample function demonstrating how to create a batch prediction job
    with real parameters (for manual testing)
    """
    # Real parameters from the existing codebase
    job_display_name = "user-embedding-batch-prediction-sample"
    model_resource_name = "projects/261231409699/locations/us-central1/models/7294814348084510720@1"
    input_gcs_path = "gs://zhang_us_central/batch_prediction/input_sample.jsonl"
    output_gcs_path = "gs://zhang_us_central/batch_prediction/output_sample/"
    
    # This would be the actual call (commented out to avoid real API calls in tests)
    """
    batch_prediction_job = aiplatform.BatchPredictionJob.create(
        job_display_name=job_display_name,
        model_name=model_resource_name,
        gcs_source=input_gcs_path,
        gcs_destination_prefix=output_gcs_path,
        machine_type="n1-standard-4",
        accelerator_type=None,  # CPU only for ONNX
        accelerator_count=0,
        starting_replica_count=1,
        max_replica_count=10,
        sync=False  # Run asynchronously
    )
    """
    
    print(f"Sample batch prediction job parameters:")
    print(f"  job_display_name: {job_display_name}")
    print(f"  model_name: {model_resource_name}")
    print(f"  gcs_source: {input_gcs_path}")
    print(f"  gcs_destination_prefix: {output_gcs_path}")
    print(f"  machine_type: n1-standard-4")
    print(f"  accelerator_type: None")
    print(f"  accelerator_count: 0")
    print(f"  starting_replica_count: 1")
    print(f"  max_replica_count: 10")
    print(f"  sync: False")


if __name__ == "__main__":
    # Run the sample function to show real parameters
    create_sample_batch_prediction_job()
    
    # Run the tests
    unittest.main()
