import os
import json
import yaml

import pandas as pd
from google.cloud import aiplatform
from google.cloud import bigquery
from google.cloud import storage

from typing import List, Dict, Any, Optional
import tempfile
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class VertexAIBatchPredictor:
    """
    Vertex AI Batch Prediction for User Embedding Generation
    """
    
    def __init__(self, project_id: str, location: str = "us-central1"):
        self.project_id = project_id
        self.location = location
        aiplatform.init(project=project_id, location=location)
        self.bq_client = bigquery.Client(project=project_id)
        self.storage_client = storage.Client(project=project_id)
    
    def export_bq_to_jsonl(self, 
                          input_table: str, 
                          output_gcs_path: str,
                          config_path: str = "model_config.yml") -> str:
        """
        Export BigQuery table to JSONL format for Vertex AI batch prediction
        
        Args:
            input_table: BigQuery table in format 'project.dataset.table'
            output_gcs_path: GCS path for output JSONL file (e.g., 'gs://bucket/path/data.jsonl')
            config_path: Path to model configuration file
            
        Returns:
            GCS path of the exported JSONL file
        """
        # Load model configuration
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        
        user_num_cols = config['model_config']['user_num_cols']
        user_cat_cols = config['model_config']['user_cat_cols']
        
        # Create the export query
        num_features_sql = ", ".join(user_num_cols)
        cat_features_sql = ", ".join(user_cat_cols)
        
        export_query = f"""
        SELECT 
            adid,
             numerical_features,
             categorical_features
        FROM `{input_table}`
        """
        
        # Export to GCS as JSONL
        job_config = bigquery.ExtractJobConfig(
            destination_format=bigquery.DestinationFormat.NEWLINE_DELIMITED_JSON
        )
        
        # Create a temporary table for the query result
        temp_table_id = f"{self.project_id}.works_zhang.batch_prediction_input_{pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')}"
        
        # Create query job
        query_job = self.bq_client.query(
            export_query,
            job_config=bigquery.QueryJobConfig(
                destination=temp_table_id,
                write_disposition=bigquery.WriteDisposition.WRITE_TRUNCATE
            )
        )
        query_job.result()  # Wait for completion
        
        # Extract to GCS
        extract_job = self.bq_client.extract_table(
            temp_table_id,
            output_gcs_path,
            job_config=job_config
        )
        extract_job.result()  # Wait for completion
        
        # Clean up temporary table
        self.bq_client.delete_table(temp_table_id)
        
        logger.info(f"Data exported to {output_gcs_path}")
        return output_gcs_path
    
    def create_batch_prediction_job(self,
                                  model_resource_name: str,
                                  input_gcs_path: str,
                                  output_gcs_path: str,
                                  job_display_name: str = "user-embedding-batch-prediction") -> str:
        """
        Create and submit a Vertex AI batch prediction job
        
        Args:
            model_resource_name: Full resource name of the deployed model
            input_gcs_path: GCS path to input JSONL file
            output_gcs_path: GCS path for output predictions
            job_display_name: Display name for the batch prediction job
            
        Returns:
            Resource name of the created batch prediction job
        """
        
        # Create batch prediction job
        batch_prediction_job = aiplatform.BatchPredictionJob.create(
            job_display_name=job_display_name,
            model_name=model_resource_name,
            gcs_source=input_gcs_path,
            gcs_destination_prefix=output_gcs_path,
            machine_type="n1-standard-4",
            accelerator_type=None,  # CPU only for ONNX
            accelerator_count=0,
            starting_replica_count=1,
            max_replica_count=10,
            sync=False  # Run asynchronously
        )
        
        logger.info(f"Batch prediction job created: {batch_prediction_job.resource_name}")
        return batch_prediction_job.resource_name
    
    def wait_for_job_completion(self, job_resource_name: str) -> Dict[str, Any]:
        """
        Wait for batch prediction job to complete and return status
        
        Args:
            job_resource_name: Resource name of the batch prediction job
            
        Returns:
            Job status and details
        """
        job = aiplatform.BatchPredictionJob(job_resource_name)
        job.wait()  # Wait for completion
        
        return {
            "state": job.state.name,
            "resource_name": job.resource_name,
            "display_name": job.display_name,
            "output_info": job.output_info
        }
    
    def load_predictions_to_bq(self,
                              prediction_gcs_path: str,
                              output_table: str,
                              schema: Optional[List] = None) -> str:
        """
        Load batch prediction results back to BigQuery
        
        Args:
            prediction_gcs_path: GCS path containing prediction results
            output_table: BigQuery table to store results (project.dataset.table)
            schema: BigQuery table schema (optional)
            
        Returns:
            Output table name
        """
        if schema is None and bigquery is not None:
            schema = [
                bigquery.SchemaField("adid", "STRING"),
                bigquery.SchemaField("user_embedding", "FLOAT", mode="REPEATED"),
                bigquery.SchemaField("prediction_timestamp", "TIMESTAMP")
            ]
        
        job_config = bigquery.LoadJobConfig(
            source_format=bigquery.SourceFormat.NEWLINE_DELIMITED_JSON,
            schema=schema,
            write_disposition=bigquery.WriteDisposition.WRITE_TRUNCATE
        )
        
        load_job = self.bq_client.load_table_from_uri(
            prediction_gcs_path,
            output_table,
            job_config=job_config
        )
        load_job.result()  # Wait for completion
        
        logger.info(f"Predictions loaded to {output_table}")
        return output_table
    
    def run_end_to_end_prediction(self,
                                 input_table: str,
                                 model_resource_name: str,
                                 output_table: str,
                                 temp_gcs_bucket: str,
                                 config_path: str = "model_config.yml") -> Dict[str, Any]:
        """
        Run end-to-end batch prediction pipeline
        
        Args:
            input_table: Input BigQuery table (project.dataset.table)
            model_resource_name: Vertex AI model resource name
            output_table: Output BigQuery table (project.dataset.table)
            temp_gcs_bucket: GCS bucket for temporary files
            config_path: Path to model configuration
            
        Returns:
            Pipeline execution results
        """
        timestamp = pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')
        
        # Step 1: Export data to GCS
        input_gcs_path = f"gs://{temp_gcs_bucket}/batch_prediction/input_{timestamp}.jsonl"
        self.export_bq_to_jsonl(input_table, input_gcs_path, config_path)
        
        # Step 2: Create batch prediction job
        output_gcs_path = f"gs://{temp_gcs_bucket}/batch_prediction/output_{timestamp}/"
        job_name = self.create_batch_prediction_job(
            model_resource_name,
            input_gcs_path,
            output_gcs_path,
            f"user-embedding-prediction-{timestamp}"
        )
        
        # Step 3: Wait for completion
        job_status = self.wait_for_job_completion(job_name)
        
        # Step 4: Load results back to BigQuery
        if job_status["state"] == "JOB_STATE_SUCCEEDED":
            prediction_files = f"{output_gcs_path}predictions_*.jsonl"
            self.load_predictions_to_bq(prediction_files, output_table)
            
            return {
                "status": "success",
                "job_details": job_status,
                "output_table": output_table,
                "input_gcs_path": input_gcs_path,
                "output_gcs_path": output_gcs_path
            }
        else:
            return {
                "status": "failed",
                "job_details": job_status
            }



