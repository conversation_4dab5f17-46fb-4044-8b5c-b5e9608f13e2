#!/usr/bin/env python3
"""
Quick script to check your specific batch prediction job logs and status
"""

from google.cloud import aiplatform
import logging

# Try to import cloud logging, but make it optional
try:
    from google.cloud import logging as cloud_logging
    CLOUD_LOGGING_AVAILABLE = True
except ImportError:
    CLOUD_LOGGING_AVAILABLE = False
    print("⚠️ Cloud Logging not available. Install with: pip install google-cloud-logging")

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Your specific job parameters
PROJECT_ID = "labs-science"
LOCATION = "us-central1"
JOB_DISPLAY_NAME = "user-embedding-batch-prediction"

def quick_job_status_check():
    """Quick check of your batch prediction job status"""
    
    # Initialize Vertex AI
    aiplatform.init(project=PROJECT_ID, location=LOCATION)
    
    print("🔍 Checking your batch prediction job...")
    print(f"Project: {PROJECT_ID}")
    print(f"Location: {LOCATION}")
    print(f"Job Display Name: {JOB_DISPLAY_NAME}")
    print("-" * 50)
    
    try:
        # Method 1: List all jobs and find yours
        jobs = aiplatform.BatchPredictionJob.list()
        
        your_job = None
        for job in jobs:
            if job.display_name == JOB_DISPLAY_NAME:
                your_job = job
                break
        
        if your_job:
            print("✅ Found your job!")
            print(f"Resource Name: {your_job.resource_name}")
            print(f"State: {your_job.state}")
            print(f"Create Time: {your_job.create_time}")
            print(f"Update Time: {your_job.update_time}")
            
            if your_job.state.name == 'JOB_STATE_SUCCEEDED':
                print("🎉 Job completed successfully!")
                if hasattr(your_job, 'output_info'):
                    print(f"Output: {your_job.output_info}")
            elif your_job.state.name == 'JOB_STATE_FAILED':
                print("❌ Job failed!")
                if hasattr(your_job, 'error'):
                    print(f"Error: {your_job.error}")
            elif your_job.state.name == 'JOB_STATE_RUNNING':
                print("🔄 Job is still running...")
            else:
                print(f"📊 Job state: {your_job.state.name}")
            
            return your_job.resource_name
        else:
            print("❌ Job not found!")
            print("Available jobs:")
            for job in jobs[:5]:
                print(f"  - {job.display_name} ({job.state.name})")
            return None
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return None


def check_cloud_logs():
    """Check Cloud Logging for your batch prediction job"""

    print("\n🔍 Checking Cloud Logs...")
    print("-" * 50)

    if not CLOUD_LOGGING_AVAILABLE:
        print("❌ Cloud Logging not available. Install with: pip install google-cloud-logging")
        return

    try:
        # Initialize Cloud Logging client
        client = cloud_logging.Client(project=PROJECT_ID)
        
        # Search for logs related to your job
        filter_str = f'''
        resource.type="aiplatform.googleapis.com/BatchPredictionJob"
        AND (jsonPayload.job_display_name="{JOB_DISPLAY_NAME}" OR textPayload:"{JOB_DISPLAY_NAME}")
        '''
        
        print(f"Searching for logs with job name: {JOB_DISPLAY_NAME}")
        
        entries = client.list_entries(filter_=filter_str, order_by=cloud_logging.DESCENDING)
        
        log_count = 0
        for entry in entries:
            log_count += 1
            print(f"\n📝 Log {log_count}:")
            print(f"Time: {entry.timestamp}")
            print(f"Severity: {entry.severity}")
            
            if hasattr(entry, 'json_payload') and entry.json_payload:
                print(f"JSON: {entry.json_payload}")
            elif hasattr(entry, 'text_payload') and entry.text_payload:
                print(f"Text: {entry.text_payload}")
            
            if log_count >= 5:  # Limit to 5 most recent logs
                break
        
        if log_count == 0:
            print("⚠️ No specific logs found. Trying broader search...")
            
            # Broader search
            broader_filter = f'''
            resource.type="aiplatform.googleapis.com/BatchPredictionJob"
            '''
            
            entries = client.list_entries(filter_=broader_filter, order_by=cloud_logging.DESCENDING)
            
            for entry in entries:
                log_count += 1
                print(f"\n📝 General Log {log_count}:")
                print(f"Time: {entry.timestamp}")
                print(f"Severity: {entry.severity}")
                
                if hasattr(entry, 'json_payload') and entry.json_payload:
                    print(f"JSON: {entry.json_payload}")
                elif hasattr(entry, 'text_payload') and entry.text_payload:
                    print(f"Text: {entry.text_payload}")
                
                if log_count >= 3:
                    break
        
    except Exception as e:
        print(f"❌ Error checking logs: {str(e)}")


def monitor_specific_job(job_resource_name):
    """Monitor a specific job by resource name"""
    
    if not job_resource_name:
        print("❌ No job resource name provided")
        return
    
    print(f"\n🔄 Monitoring job: {job_resource_name}")
    print("-" * 50)
    
    try:
        job = aiplatform.BatchPredictionJob(job_resource_name)
        
        print(f"Current state: {job.state}")
        print(f"Last update: {job.update_time}")
        
        # If you want to wait for completion (be careful - this can take a long time!)
        # job.wait()  # Uncomment this line to wait for job completion
        
        return job
        
    except Exception as e:
        print(f"❌ Error monitoring job: {str(e)}")
        return None


if __name__ == "__main__":
    print("🚀 Quick Batch Prediction Job Checker")
    print("=" * 60)
    
    # Step 1: Check job status
    job_resource_name = quick_job_status_check()
    
    # Step 2: Check logs
    check_cloud_logs()
    
    # Step 3: Show how to monitor
    if job_resource_name:
        print(f"\n💡 To monitor this job continuously, run:")
        print(f"monitor_specific_job('{job_resource_name}')")
        
        print(f"\n💡 To wait for job completion in your code, use:")
        print(f"job = aiplatform.BatchPredictionJob('{job_resource_name}')")
        print(f"job.wait()  # This will block until job completes")
    
    print("\n" + "=" * 60)
    print("✅ Quick check completed!")
